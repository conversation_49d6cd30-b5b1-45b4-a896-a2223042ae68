import React from "react";
import ProductSection from "@/components/homePage/products/ProductSection";
import { PRODUCT_SECTIONS } from "@/lib/constants";

const Product = () => {
  return (
    <div className="w-full flex items-center flex-col justify-center px-4 py-16 lg:px-8 lg:py-24 gap-12">
      <div className="text-center space-y-4 max-w-3xl">
        <h1 className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-white to-gray-400  bg-clip-text text-transparent">
          Professional Product Photography just got 10x faster and affordable
        </h1>
        <p className="text-lg text-muted-foreground">
          Transform your visuals with AI-powered enhancement technology
        </p>
      </div>

      <div className="w-full max-w-7xl mx-auto rounded-2xl border border-gray-600 bg-gradient-to-b from-white/5 to-transparent backdrop-blur-sm p-2 sm:p-8 lg:p-12">
        {PRODUCT_SECTIONS.map((section, index) => (
          <React.Fragment key={section.id}>
            <ProductSection {...section} />
            {index !== PRODUCT_SECTIONS.length - 1 && (
              <div className="w-full h-px bg-gradient-to-r from-transparent via-primary/20 to-transparent my-10" />
            )}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
};

export default Product;
